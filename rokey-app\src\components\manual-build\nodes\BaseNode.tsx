'use client';

import { Handle, Position } from '@xyflow/react';
import { ReactNode } from 'react';
import { ArrowRightIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { WorkflowNode } from '@/types/manualBuild';

interface BaseNodeProps {
  data: WorkflowNode['data'];
  children?: ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  hasInput?: boolean;
  hasOutput?: boolean;
  hasRoleInput?: boolean;
  hasToolsInput?: boolean;
  inputLabel?: string;
  outputLabel?: string;
  roleInputLabel?: string;
  toolsInputLabel?: string;
  className?: string;
}

export default function BaseNode({
  data,
  children,
  icon: Icon,
  color = '#ff6b35',
  hasInput = true,
  hasOutput = true,
  hasRoleInput = false,
  hasToolsInput = false,
  inputLabel = 'Input',
  outputLabel = 'Output',
  roleInputLabel = 'Role',
  toolsInputLabel = 'Tools',
  className = ''
}: BaseNodeProps) {
  const isConfigured = data.isConfigured;
  const hasError = data.hasError;

  return (
    <div className={`relative ${className}`}>
      {/* Input Handle with Label */}
      {hasInput && (
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 flex items-center z-10">
          <Handle
            type="target"
            position={Position.Left}
            id="input"
            className="w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors"
            style={{ left: -8 }}
          />
          <div className="ml-2 px-2 py-1 bg-gray-800/95 border border-gray-600 rounded text-xs text-gray-300 font-medium shadow-lg">
            {inputLabel}
          </div>
        </div>
      )}

      {/* Role Input Handle with Label */}
      {hasRoleInput && (
        <div className="absolute left-0 transform -translate-y-1/2 flex items-center z-10" style={{ top: '30%' }}>
          <Handle
            type="target"
            position={Position.Left}
            id="role"
            className="w-4 h-4 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors"
            style={{ left: -8 }}
          />
          <div className="ml-2 px-2 py-1 bg-purple-900/95 border border-purple-600 rounded text-xs text-purple-200 font-medium shadow-lg">
            {roleInputLabel}
          </div>
        </div>
      )}

      {/* Tools Input Handle with Label */}
      {hasToolsInput && (
        <div className="absolute left-0 transform -translate-y-1/2 flex items-center z-10" style={{ top: '70%' }}>
          <Handle
            type="target"
            position={Position.Left}
            id="tools"
            className="w-4 h-4 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors"
            style={{ left: -8 }}
          />
          <div className="ml-2 px-2 py-1 bg-green-900/95 border border-green-600 rounded text-xs text-green-200 font-medium shadow-lg">
            {toolsInputLabel}
          </div>
        </div>
      )}

      {/* Node Body */}
      <div
        className={`min-w-[200px] rounded-lg border-2 transition-all duration-200 ${
          hasError
            ? 'border-red-500 bg-red-900/20'
            : isConfigured
            ? 'border-gray-600 bg-gray-800/90'
            : 'border-yellow-500 bg-yellow-900/20'
        } backdrop-blur-sm shadow-lg hover:shadow-xl`}
        style={{
          borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'
        }}
      >
        {/* Header */}
        <div 
          className="px-4 py-3 rounded-t-lg flex items-center gap-3"
          style={{
            background: hasError 
              ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))'
              : `linear-gradient(135deg, ${color}20, ${color}10)`
          }}
        >
          {Icon && (
            <div 
              className="p-2 rounded-lg"
              style={{
                backgroundColor: hasError ? '#ef444420' : `${color}20`,
                color: hasError ? '#ef4444' : color
              }}
            >
              <Icon className="w-4 h-4" />
            </div>
          )}
          <div className="flex-1">
            <div className="font-medium text-white text-sm">
              {data.label}
            </div>
            {data.description && (
              <div className="text-xs text-gray-400 mt-1">
                {data.description}
              </div>
            )}
          </div>
          
          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            {hasError ? (
              <div className="w-2 h-2 bg-red-500 rounded-full" title="Error" />
            ) : isConfigured ? (
              <div className="w-2 h-2 bg-green-500 rounded-full" title="Configured" />
            ) : (
              <div className="w-2 h-2 bg-yellow-500 rounded-full" title="Needs configuration" />
            )}
          </div>
        </div>

        {/* Input Connection Indicators */}
        {(hasInput || hasRoleInput) && (
          <div className="absolute -left-16 top-0 h-full flex flex-col justify-center space-y-2 pointer-events-none">
            {hasRoleInput && (
              <div className="flex items-center">
                <div className="flex items-center gap-1 text-xs text-purple-300 bg-purple-900/90 border border-purple-700/50 px-2 py-1 rounded-lg shadow-lg">
                  <UserGroupIcon className="w-3 h-3" />
                  Role
                </div>
                <div className="w-3 h-0.5 bg-purple-500/50 ml-1"></div>
              </div>
            )}
            {hasInput && (
              <div className="flex items-center">
                <div className="flex items-center gap-1 text-xs text-gray-300 bg-gray-800/90 border border-gray-600/50 px-2 py-1 rounded-lg shadow-lg">
                  <ArrowRightIcon className="w-3 h-3" />
                  Data
                </div>
                <div className="w-3 h-0.5 bg-gray-500/50 ml-1"></div>
              </div>
            )}
          </div>
        )}

        {/* Content */}
        {children && (
          <div className="px-4 py-3 border-t border-gray-700/50">
            {children}
          </div>
        )}

        {/* Error Message */}
        {hasError && data.errorMessage && (
          <div className="px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg">
            <div className="text-xs text-red-300">
              {data.errorMessage}
            </div>
          </div>
        )}
      </div>

      {/* Output Handle */}
      {hasOutput && (
        <Handle
          type="source"
          position={Position.Right}
          className="w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors"
          style={{ right: -8 }}
        />
      )}
    </div>
  );
}
